import TasksCard from "./TasksCard";
export default function Columns({ column, tasks }) {
  return (
    <>
      <div className="flex w-80 flex-col rounded-lg bg-neutral-800 p-4">
        <div className="mb-4 font-semibold text-neutral-100">{column.title}</div>
        <div className="flex flex-1 flex-col gap-4">
          {tasks.map((tasks) => {
            <TasksCard tasks={tasks} key={tasks.id} />;
          })}
        </div>
      </div>
    </>
  );
}
