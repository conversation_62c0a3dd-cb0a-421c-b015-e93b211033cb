export default function Home() {
  return (
    <>
      <div className="">
        <h1 className="text-center font-bold text-3xl">Content</h1>
      </div>
      <div className="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table className="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
          <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
              <th scope="col" className="px-6 py-3">
                Content
              </th>
              <th scope="col" className="px-6 py-3">
                Link
              </th>
            </tr>
          </thead>
          <tbody>
            <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
              <td className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">learn_drag_n_drop</td>
              <td className="px-6 py-4">
                <a href="/learnDragnDrop" className="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                  Drag N Drop
                </a>
              </td>
            </tr>
            <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
              <td className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">another_item</td>
              <td className="px-6 py-4">
                <a href="/anotherItem" className="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                  View Item
                </a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </>
  );
}
